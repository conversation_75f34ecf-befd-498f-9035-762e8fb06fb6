#!/usr/bin/env python3
"""
Demo script to showcase the new multi-threading GUI controls
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def demo_gui():
    """Launch the GUI and demonstrate threading controls"""
    print("🚀 Launching Email Batch Processor with Multi-threading Support")
    print("=" * 60)
    print("New Features Added:")
    print("✅ Multi-threading checkbox for large batch processing")
    print("✅ Thread count spinbox (1-8 threads)")
    print("✅ Memory usage estimation")
    print("✅ Intelligent batch size detection")
    print("✅ Performance monitoring and timing")
    print("✅ Automatic threading suggestions for large batches")
    print("=" * 60)
    
    try:
        from email_processor import EmailProcessorGUI
        
        app = QApplication(sys.argv)
        
        # Set application properties
        app.setApplicationName("Email Batch Processor")
        app.setApplicationVersion("2.0 - Multi-threading Edition")
        app.setOrganizationName("Email Tools")
        
        # Create and show main window
        window = EmailProcessorGUI()
        window.show()
        
        # Print instructions
        print("\n📋 How to test the new multi-threading features:")
        print("1. Select 5+ .eml files to trigger threading suggestion")
        print("2. Check the 'Enable multi-threading' checkbox")
        print("3. Adjust the worker thread count (1-8)")
        print("4. Notice the memory usage estimate updates")
        print("5. Start processing to see performance improvements")
        print("6. Check the logs for threading status and performance metrics")
        print("\n🎯 The GUI is now ready for testing!")
        
        # Run the application
        return app.exec()
        
    except Exception as e:
        print(f"❌ Failed to launch GUI: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(demo_gui())
