#!/usr/bin/env python3
"""
Quick GUI layout test to verify header compression and memory display
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_gui_layout():
    """Launch GUI briefly to verify layout changes"""
    print("🖥️ Testing GUI Layout Changes...")
    print("=" * 50)
    
    try:
        from email_processor import EmailProcessorGUI
        
        app = QApplication(sys.argv)
        
        # Create and show main window
        window = EmailProcessorGUI()
        window.show()
        
        print("✅ GUI launched successfully")
        print("📋 Verifying layout improvements:")
        print("  • Header section should be more compact")
        print("  • Title should be smaller (14pt font)")
        print("  • Subtitle should be completely removed")
        print("  • More space available for processing log")
        print("  • Memory usage should show realistic estimates")
        
        # Test memory usage display updates
        print("\n🧮 Testing memory usage display:")
        for threads in [1, 2, 4, 6, 8]:
            window.thread_count_spinbox.setValue(threads)
            window.update_memory_estimate()
            memory_text = window.memory_label.text()
            print(f"  {threads} threads: {memory_text}")
        
        print("\n✅ GUI layout test completed!")
        print("💡 The GUI window is now open for visual inspection.")
        print("   You can verify:")
        print("   - Compressed header takes less vertical space")
        print("   - Memory estimates are realistic (1.1-8.9 GB range)")
        print("   - Processing log area has more visible space")
        print("   - Multi-threading checkbox is properly displayed")
        
        # Keep window open for a few seconds for inspection
        QTimer.singleShot(5000, app.quit)  # Auto-close after 5 seconds
        
        return app.exec()
        
    except Exception as e:
        print(f"❌ GUI layout test failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(test_gui_layout())
