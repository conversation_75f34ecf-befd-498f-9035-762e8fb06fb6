#!/usr/bin/env python3
"""
Test script for multi-threading functionality in Email Batch Processor
"""

import sys
import os

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_threading_configuration():
    """Test the ThreadingConfiguration class"""
    print("Testing ThreadingConfiguration...")
    
    try:
        from email_processor import ThreadingConfiguration
        
        # Test optimal thread count
        optimal_threads = ThreadingConfiguration.get_optimal_thread_count()
        print(f"✅ Optimal thread count: {optimal_threads}")
        assert 1 <= optimal_threads <= 4, f"Thread count should be between 1-4, got {optimal_threads}"
        
        # Test memory calculation
        memory_usage = ThreadingConfiguration.calculate_memory_usage(4)
        print(f"✅ Memory usage for 4 threads: {memory_usage:.1f} GB")
        assert memory_usage == 0.4, f"Expected 0.4 GB for 4 threads, got {memory_usage}"
        
        # Test threading benefit detection
        assert ThreadingConfiguration.is_threading_beneficial(5) == True
        assert ThreadingConfiguration.is_threading_beneficial(3) == False
        print("✅ Threading benefit detection works correctly")
        
        print("✅ ThreadingConfiguration tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ ThreadingConfiguration test failed: {e}")
        return False

def test_thread_local_ocr():
    """Test the ThreadLocalOCR class"""
    print("\nTesting ThreadLocalOCR...")
    
    try:
        from email_processor import ThreadLocalOCR, OCR_SERVICE_AVAILABLE
        
        # Create thread-local OCR manager
        ocr_manager = ThreadLocalOCR()
        
        # Test getting OCR service
        ocr_service = ocr_manager.get_ocr_service()
        
        if OCR_SERVICE_AVAILABLE:
            print("✅ OCR service is available")
            assert ocr_service is not None, "OCR service should not be None when available"
        else:
            print("⚠️ OCR service is not available (PaddleOCR not installed)")
            assert ocr_service is None, "OCR service should be None when not available"
        
        print("✅ ThreadLocalOCR tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ ThreadLocalOCR test failed: {e}")
        return False

def test_processing_threads():
    """Test that both ProcessingThread and ThreadedProcessingThread can be imported"""
    print("\nTesting ProcessingThread classes...")
    
    try:
        from email_processor import ProcessingThread, ThreadedProcessingThread
        
        # Test that both classes have the required signals
        required_signals = ['progress_updated', 'status_updated', 'file_processed', 
                          'finished_processing', 'error_occurred']
        
        for signal_name in required_signals:
            assert hasattr(ProcessingThread, signal_name), f"ProcessingThread missing {signal_name}"
            assert hasattr(ThreadedProcessingThread, signal_name), f"ThreadedProcessingThread missing {signal_name}"
        
        print("✅ Both ProcessingThread classes have required signals")
        
        # Test ThreadedProcessingThread initialization
        test_files = ['test1.eml', 'test2.eml']
        test_output = 'test_output.xlsx'
        
        # This should not fail during initialization
        threaded_processor = ThreadedProcessingThread(test_files, test_output, max_workers=2)
        assert threaded_processor.max_workers == 2
        assert threaded_processor.file_paths == test_files
        assert threaded_processor.output_path == test_output
        
        print("✅ ThreadedProcessingThread initialization works")
        print("✅ ProcessingThread tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ ProcessingThread test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Email Batch Processor Multi-threading Implementation")
    print("=" * 60)
    
    tests = [
        test_threading_configuration,
        test_thread_local_ocr,
        test_processing_threads
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Multi-threading implementation is ready.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
